"""
向量数据库连接和管理Repository

负责：
- 数据库连接管理
- 数据库创建和删除
- 连接状态监控
- 数据库配置管理
"""

import time
from typing import Optional, Dict, Any
from app.core.vectordb import VectorDatabase, MilvusVectorDB
from app.config.settings import VectorDBConfig


class VectorDatabaseRepository:
    """向量数据库连接和管理Repository"""
    
    def __init__(self):
        self._connections: Dict[str, VectorDatabase] = {}
        self._config: Optional[VectorDBConfig] = None
    
    def set_config(self, config: VectorDBConfig) -> None:
        """设置数据库配置"""
        self._config = config
    
    async def get_database_connection(
        self, 
        database: Optional[str] = None, 
        create_if_not_exists: bool = False
    ) -> VectorDatabase:
        """
        获取数据库连接
        
        Args:
            database: 数据库名称，为None时使用默认数据库
            create_if_not_exists: 如果数据库不存在是否创建
            
        Returns:
            VectorDatabase: 数据库连接实例
            
        Raises:
            Exception: 连接失败或配置错误
        """
        if not self._config:
            raise Exception("数据库配置未设置，请先调用set_config方法")
        
        # 生成连接标识符
        connection_key = database or "default"
        
        # 如果已有连接，直接返回
        if connection_key in self._connections:
            print(f"[数据库] 复用现有连接: {connection_key}")
            return self._connections[connection_key]
        
        # 创建新连接
        print(f"[数据库] 创建新连接: {connection_key}")
        db_instance = await self._create_connection(database, create_if_not_exists)
        
        # 缓存连接
        self._connections[connection_key] = db_instance
        
        return db_instance
    
    async def _create_connection(
        self, 
        database: Optional[str], 
        create_if_not_exists: bool
    ) -> VectorDatabase:
        """创建数据库连接"""
        if not self._config:
            raise Exception("数据库配置未设置")
            
        connect_start = time.time()
        
        if self._config.db_type == 'milvus':
            print(f"[连接] 使用URI方式连接Milvus: {self._config.uri}")
            if database:
                print(f"[连接] 使用数据库: {database}")
            else:
                print("[连接] 使用默认数据库")

            # 创建实例
            instance_start = time.time()
            # 处理token参数
            token = ""
            if self._config.token and len(self._config.token.strip()) > 0:
                token = self._config.token
            
            db_instance = MilvusVectorDB(
                uri=self._config.uri,
                token=token,
                database=database or ""
            )
            instance_time = time.time() - instance_start
            print(f"[时间] 创建Milvus实例耗时: {instance_time:.3f}秒")

            # 连接到数据库
            try:
                connect_db_start = time.time()
                await db_instance.connect()
                connect_db_time = time.time() - connect_db_start
                print(f"[成功] 成功连接到Milvus数据库, 耗时: {connect_db_time:.3f}秒")
                print(f"[时间] 数据库连接详情:")
                print(f"  - 创建实例: {instance_time:.3f}秒")
                print(f"  - 建立连接: {connect_db_time:.3f}秒")
                print(f"  - 总连接时间: {time.time() - connect_start:.3f}秒")
            except Exception as conn_error:
                if create_if_not_exists and database and "database not found" in str(conn_error).lower():
                    try:
                        print(f"[信息] 数据库 '{database}' 不存在，尝试创建...")
                        # 先连接到默认数据库
                        default_db_instance = MilvusVectorDB(
                            uri=self._config.uri,
                            token=token,
                            database=""
                        )
                        await default_db_instance.connect()

                        # 创建新数据库
                        await default_db_instance.create_database(database)
                        print(f"[成功] 成功创建数据库 '{database}'")

                        # 重新连接到新创建的数据库
                        await db_instance.connect()
                        print(f"[成功] 成功连接到新创建的数据库 '{database}'")
                    except Exception as create_error:
                        print(f"[错误] 创建数据库失败: {create_error}")
                        raise Exception(f"创建数据库失败: {create_error}")
                else:
                    print(f"[错误] Milvus数据库连接失败: {conn_error}")
                    raise Exception(f"Milvus连接失败: {conn_error}")

            return db_instance
        else:
            raise Exception(f"不支持的数据库类型: {self._config.db_type}")
    
    async def create_database(self, database_name: str) -> None:
        """
        创建数据库
        
        Args:
            database_name: 数据库名称
            
        Raises:
            Exception: 创建失败
        """
        if not self._config:
            raise Exception("数据库配置未设置")
        
        print(f"[数据库] 创建数据库: {database_name}")
        
        # 使用默认数据库连接来创建新数据库
        default_db = await self.get_database_connection()
        if hasattr(default_db, 'create_database'):
            create_db_method = getattr(default_db, 'create_database')
            await create_db_method(database_name)
        else:
            raise Exception("当前数据库不支持创建数据库操作")
        
        print(f"[成功] 数据库 '{database_name}' 创建成功")
    
    async def delete_database(self, database_name: str) -> None:
        """
        删除数据库
        
        Args:
            database_name: 数据库名称
            
        Raises:
            Exception: 删除失败
        """
        if not self._config:
            raise Exception("数据库配置未设置")
        
        print(f"[数据库] 删除数据库: {database_name}")
        
        # 使用默认数据库连接来删除数据库
        default_db = await self.get_database_connection()
        if hasattr(default_db, 'drop_database'):
            drop_db_method = getattr(default_db, 'drop_database')
            await drop_db_method(database_name)
        else:
            raise Exception("当前数据库不支持删除数据库操作")
        
        # 清理缓存的连接
        if database_name in self._connections:
            del self._connections[database_name]
        
        print(f"[成功] 数据库 '{database_name}' 删除成功")
    
    def close_connection(self, database: Optional[str] = None) -> None:
        """
        关闭数据库连接
        
        Args:
            database: 数据库名称，为None时关闭默认数据库连接
        """
        connection_key = database or "default"
        
        if connection_key in self._connections:
            print(f"[数据库] 关闭连接: {connection_key}")
            # 这里可以添加实际的连接关闭逻辑
            del self._connections[connection_key]
    
    def close_all_connections(self) -> None:
        """关闭所有数据库连接"""
        print(f"[数据库] 关闭所有连接，共 {len(self._connections)} 个")
        self._connections.clear()
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "active_connections": len(self._connections),
            "connection_keys": list(self._connections.keys()),
            "config_type": self._config.db_type if self._config else None
        } 