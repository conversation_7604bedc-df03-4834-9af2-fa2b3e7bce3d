import asyncio
import json
import requests


# 服务地址
# API_URL = "http://localhost:8000/api/v1/upload_texts"
API_URL = "http://*************:8000/api/v1/upload_texts"
# API_URL = "https://copilot.csvw.com/rag_service/api/v1/upload_texts"

async def test_upload_texts():
    print("测试文本列表上传接口...")
    
    # 准备测试数据
    test_data = {
        "texts": ["土方啊啊啊啊"],
        "database": "lkz_testasdasda",
        "collection": "aasdaasdasdasdsda",
        "unit": "m1",
        # "encrypt": True,
        "embedding_type": "azure-openai",  # 使用Azure OpenAI生成向量
        "metadata": {
            "engineering_project": "项目1",
            "project_description": "1、部位：综合所有部位； \
            2、不论土质类别(包含但不限于地勘报告描述的杂填土、素填土、粉质粘土等)、不论场内转运运距、不论施工方法、不论场内转运次数； \
            3、含挖、运、装、堆土、填土、摊平、压实、整理等全部工作内容； \
            4、回填土土质必须满足设计及相关规范要求，压实度≥95%； \
            5、清单工程量按定额工程量计算规则计算。"
        }
    }
    
    # 发送请求
    try:
        response = requests.post(API_URL, json=test_data, verify=False)
        
        # 打印响应
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            print(f"成功上传 {result.get('text_count', 0)} 条文本")
            print(f"文档ID: {result.get('doc_id', '')}")
            print(f"向量维度: {result.get('vector_dimension', 0)}")
        else:
            print(f"错误响应: {response.text}")
    except Exception as e:
        print(f"请求出错: {e}")
    
    print("测试完成")

if __name__ == "__main__":
    import time
    t1 = time.time()
    asyncio.run(test_upload_texts())
    t2 = time.time()
    print(f"请求耗时: {t2 - t1:.2f}秒")
