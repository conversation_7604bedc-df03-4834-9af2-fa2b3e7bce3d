from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
import time
import asyncio
from datetime import datetime
from app.utils.logging_utils import OperationLog<PERSON>, DebugLogger, TimeTracker, _log_manager

class VectorDatabase(ABC):
    @abstractmethod
    async def connect(self):
        pass

    @abstractmethod
    async def create_collection(self, name: str, dim: int):
        pass

    @abstractmethod
    async def insert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        pass

    @abstractmethod
    async def search_vectors(self, collection: str, vector: List[float], top_k: int, filter_expr: str = "") -> List[Dict]:
        pass

    @abstractmethod
    async def search_hybrid_vectors(
        self,
        collection: str,
        query_text: str,
        dense_vector: List[float],
        top_k: int,
        filter_expr: str = "",
        search_strategy: str = "hybrid",
        rerank_strategy: str = "rrf",
        rrf_k: int = 60,
        dense_weight: float = 0.6,
        sparse_weight: float = 0.4
    ) -> List[Dict]:
        """混合检索：结合语义检索和全文检索"""
        pass

    @abstractmethod
    async def upsert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """更新或插入向量记录"""
        pass

    @abstractmethod
    async def delete_vectors(self, collection: str, ids: Optional[List[Any]] = None, filter_expr: str = ""):
        """删除向量记录，可以按ID或按条件删除"""
        pass

    @abstractmethod
    async def flush_collection(self, collection: str):
        """刷新集合数据，确保数据持久化"""
        pass

    @abstractmethod
    async def create_hybrid_collection(
        self, 
        collection_name: str, 
        dense_vector_dim: int, 
        auto_id: bool = True, 
        enable_dynamic_field: bool = True
    ):
        """创建支持混合检索的集合"""
        pass

    @abstractmethod
    async def insert_hybrid_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """插入混合检索向量到指定集合
        
        记录应包含:
        - content: 文本内容（用于存储和全文检索，非加密）
        - vector: 密集向量（用于语义检索）
        - metadata: JSON格式的元数据
        """
        pass

# 全局连接池
_connection_pool: Dict[str, Tuple[Any, float]] = {}  # {key: (client, last_used_time)}
_pool_lock = asyncio.Lock()  # 防止并发问题
_pool_max_size = 3  # 最大连接数（根据您的需求设置为3）
_pool_health_check_interval = 60  # 健康检查间隔（秒）
_last_health_check = time.time()  # 上次健康检查时间

class MilvusVectorDB(VectorDatabase):
    def __init__(self, uri: str, token: str, database: str):
        # milvus_client连接方式参数
        self.uri = uri
        self.token = token
        # 数据库名称
        self.database = database
        self.client = None
        self.collections_cache = set()

    @classmethod
    async def _check_connection_health(cls, client):
        """检查连接是否健康"""
        try:
            # 执行一个简单的操作来验证连接
            await client.list_collections()
            return True
        except Exception as e:
            OperationLogger.log_operation_error("连接健康检查", e, category="database.pool")
            return False

    @classmethod
    async def _perform_pool_health_check(cls):
        """执行连接池健康检查"""
        global _last_health_check
        current_time = time.time()

        # 检查是否需要进行健康检查
        if current_time - _last_health_check < _pool_health_check_interval:
            return

        _last_health_check = current_time
        OperationLogger.log_operation_start("连接池健康检查", category="database.pool", details=f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        async with _pool_lock:
            # 检查所有连接的健康状态
            unhealthy_keys = []
            for key, (client, _) in _connection_pool.items():
                if not await cls._check_connection_health(client):
                    unhealthy_keys.append(key)

            # 移除不健康的连接
            for key in unhealthy_keys:
                DebugLogger.log_debug_info("移除不健康的连接", f"key: {key}", category="database.pool")
                del _connection_pool[key]

        OperationLogger.log_operation_success("连接池健康检查", category="database.pool", details=f"当前连接池大小: {len(_connection_pool)}")

    @classmethod
    async def get_connection(cls, uri: str, token: Optional[str] = None, database: Optional[str] = None):
        """从连接池获取连接，如果不存在或无效则创建新连接"""
        # 执行连接池健康检查
        await cls._perform_pool_health_check()

        pool_key = f"{uri}:{database or 'default'}"

        async with _pool_lock:  # 防止并发问题
            # 尝试从池中获取连接
            if pool_key in _connection_pool:
                client, _ = _connection_pool[pool_key]

                # 验证连接是否有效
                if await cls._check_connection_health(client):
                    # 更新最后使用时间
                    _connection_pool[pool_key] = (client, time.time())
                    DebugLogger.log_debug_info("使用现有连接", f"key: {pool_key}", "database.pool")
                    return client
                else:
                    OperationLogger.log_step("连接失效", details=f"移除失效连接: {pool_key}", category="database.pool")
                    # 连接无效，从池中移除
                    del _connection_pool[pool_key]

            # 检查连接池大小
            if len(_connection_pool) >= _pool_max_size:
                # 移除最旧的连接
                oldest_key = min(_connection_pool.items(), key=lambda x: x[1][1])[0]
                del _connection_pool[oldest_key]
                DebugLogger.log_debug_info("连接池已满", f"移除最旧连接: {oldest_key}", "database.pool")

            # 创建新连接
            OperationLogger.log_step("创建新连接", details=f"key: {pool_key}", category="database.pool")
            from pymilvus import AsyncMilvusClient

            try:
                if database and len(database.strip()) > 0:
                    # 构建客户端参数，避免传递None值给token
                    client_params = {
                        "uri": uri,
                        "db_name": database,
                        "timeout": 300  # 5分钟超时
                    }
                    if token and len(token.strip()) > 0:
                        client_params["token"] = token

                    client = AsyncMilvusClient(**client_params)
                else:
                    # 构建客户端参数，避免传递None值给token
                    client_params = {
                        "uri": uri,
                        "timeout": 300  # 5分钟超时
                    }
                    if token and len(token.strip()) > 0:
                        client_params["token"] = token

                    client = AsyncMilvusClient(**client_params)

                # 将新连接添加到连接池
                _connection_pool[pool_key] = (client, time.time())
                OperationLogger.log_operation_success("创建新连接", "database.pool", key=pool_key)
                return client
            except Exception as e:
                OperationLogger.log_operation_error("创建新连接", e, "database.pool", key=pool_key)
                raise

    async def connect(self):
        """连接到Milvus数据库，使用连接池获取连接"""
        connect_start = time.time()
        try:
            OperationLogger.log_connection_attempt(self.database, "database")

            # 使用连接池获取客户端
            self.client = await self.get_connection(
                uri=self.uri,
                token=self.token,
                database=self.database
            )

            connect_time = time.time() - connect_start
            OperationLogger.log_connection_success(self.database, connection_time=connect_time, category="database")
        except Exception as e:
            OperationLogger.log_connection_error(self.database, error=e, category="database")
            raise

        # 初始化后获取所有集合名称
        await self._refresh_collections()

    async def _refresh_collections(self):
        """刷新集合列表缓存"""
        try:
            if not self.client:
                await self.connect()

            if self.client:
                collections = await self.client.list_collections()
                self.collections_cache = set(collections)
                DebugLogger.log_debug_info(tag="刷新集合缓存", info=f"可用集合: {self.collections_cache}", category="database")
            else:
                OperationLogger.log_operation_error("刷新集合列表", Exception("数据库客户端未初始化"), "database")
                self.collections_cache = set()
        except Exception as e:
            OperationLogger.log_operation_error("刷新集合列表", e, "database")
            self.collections_cache = set()

    async def has_collection(self, collection_name: str) -> bool:
        """
        检查集合是否存在
        
        Args:
            collection_name: 集合名称
          
        Returns:
            bool: 集合是否存在
        """
        try:
            if self.client is None:
                await self.connect()

            # 确保client已初始化
            if self.client is None:
                raise Exception("无法连接到数据库")

            if hasattr(self.client, 'has_collection'):
                return await self.client.has_collection(collection_name)
            else:
                # 备选方案：检查集合是否在缓存列表中
                await self._refresh_collections()
                return collection_name in self.collections_cache
        except Exception as e:
            OperationLogger.log_operation_error(f"检查集合是否存在: {collection_name}", e, "database")
            return False

    async def describe_collection(self, collection_name: str):
        """
        获取集合的详细信息

        Args:
            collection_name: 集合名称

        Returns:
            集合信息字典

        Raises:
            Exception: 获取失败
        """
        try:
            if self.client is None:
                await self.connect()

            # 确保client已初始化
            if self.client is None:
                raise Exception("无法连接到数据库")

            # AsyncMilvusClient的方法都是异步的
            return await self.client.describe_collection(collection_name)
        except Exception as e:
            OperationLogger.log_operation_error(f"获取集合信息: {collection_name}", e, "database")
            raise Exception(f"获取集合信息失败: {str(e)}")

    async def create_database(self, db_name: str):
        """创建新的数据库"""
        try:
            # 检查客户端是否初始化
            if self.client is None:
                raise ValueError("Milvus client is not initialized. Connection may have failed.")

            # 获取现有数据库列表
            try:
                databases = await self.client.list_databases()
                DebugLogger.log_debug_info("可用数据库", f"{databases}", "database")

                # 检查数据库是否已存在
                if db_name in databases:
                    DebugLogger.log_debug_info("数据库已存在", db_name, "database")
                    return db_name
            except Exception as e:
                _log_manager.log("WARN", "database", "无法列出数据库", details=str(e), action="step")
                # 继续尝试创建数据库

            # 创建新数据库
            try:
                await self.client.create_database(db_name=db_name)
                OperationLogger.log_operation_success("创建数据库", "database", database_name=db_name)
                return db_name
            except Exception as e:
                # 如果创建失败，检查是否是因为数据库已存在
                if "already exists" in str(e).lower():
                    DebugLogger.log_debug_info("数据库已存在", db_name, "database")
                    return db_name
                else:
                    OperationLogger.log_operation_error("创建数据库", e, "database", database_name=db_name)
                    raise
        except Exception as e:
            OperationLogger.log_operation_error("创建数据库", e, "database", database_name=db_name)
            raise

    async def create_collection(self, name: str, dim: int, auto_id: bool = True, enable_dynamic_field: bool = True):
        """创建集合，如果已存在则加载

        Args:
            name: 集合名称
            dim: 向量维度
            auto_id: 是否使用自增ID，默认为True
            enable_dynamic_field: 是否启用动态字段，默认为True
        """
        # 确保已连接到数据库
        if self.client is None:
            DebugLogger.log_debug_info("数据库未连接", "将在创建集合前自动连接", "database")
            await self.connect()

        # 使用client方式创建集合
        try:
            # 检查集合是否已存在
            if self.client is None:
                raise ValueError("Milvus client is not initialized. Connection may have failed.")

            collections = await self.client.list_collections()
            if name in collections:
                DebugLogger.log_debug_info(tag=f"集合 '{name}' 已存在", info="跳过创建", category="database")
                return name

            # 创建新集合，使用明确的schema定义JSON字段
            try:
                from pymilvus import DataType

                # 创建schema
                schema = self.client.create_schema(
                    auto_id=auto_id,
                    enable_dynamic_field=enable_dynamic_field,
                    description="Text embedding collection with JSON metadata"
                )

                # 添加字段 - 支持字符串和整数ID
                if auto_id:
                    # 自增ID模式使用INT64
                    schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=auto_id)
                else:
                    # 自定义ID模式使用VARCHAR以支持字符串和数字
                    schema.add_field(field_name="id", datatype=DataType.VARCHAR, max_length=255, is_primary=True, auto_id=auto_id)
                
                # 添加其他必需字段（对于auto_id和自定义id都需要）
                schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=dim)
                schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=65535)
                schema.add_field(field_name="collection_name", datatype=DataType.VARCHAR, max_length=255)
                schema.add_field(field_name="metadata", datatype=DataType.JSON)  # 使用原生JSON字段

                # 创建索引参数
                index_params = self.client.prepare_index_params()

                # 为向量字段创建索引
                index_params.add_index(
                    field_name="vector",
                    index_type="AUTOINDEX",
                    metric_type="COSINE"
                )

                # 可选：为JSON字段的常用路径创建索引以提升查询性能
                # 这里可以根据实际使用情况添加特定路径的索引
                # 例如：为metadata中的category字段创建索引
                # index_params.add_index(
                #     field_name="metadata",
                #     index_type="INVERTED",
                #     index_name="metadata_category_index",
                #     params={
                #         "json_path": "metadata[\"category\"]",
                #         "json_cast_type": "varchar"
                #     }
                # )

                # 创建集合
                await self.client.create_collection(
                    collection_name=name,
                    schema=schema,
                    index_params=index_params
                )
                OperationLogger.log_operation_success(f"创建集合 '{name}'", "database", details="使用JSON元数据字段")

            except Exception as e:
                OperationLogger.log_operation_error(f"创建集合 '{name}'", e, "database")
                raise

            # 加载集合
            try:
                if hasattr(self.client, 'load_collection'):
                    await self.client.load_collection(collection_name=name)
                    DebugLogger.log_debug_info(tag=f"已加载集合 '{name}'", info="", category="database")
            except AttributeError as e:
                DebugLogger.log_debug_info(tag="load_collection方法不可用", info=str(e), category="database")
                # 在某些版本的MilvusClient中，创建集合后会自动加载，不需要显式调用load_collection

            # 更新集合缓存
            self.collections_cache.add(name)
            OperationLogger.log_operation_success(f"完成集合 '{name}' 的创建流程", "database")
            return name
        except Exception as e:
            OperationLogger.log_operation_error(f"创建集合 '{name}' 流程失败", e, "database")
            raise

    async def insert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """插入向量到指定集合"""
        if not records:
            DebugLogger.log_debug_info(f"无记录可插入", f"集合: {collection}", "database")
            return 0

        # 确保集合存在
        if collection not in self.collections_cache:
            if 'vector' in records[0]:
                dim = len(records[0]['vector'])
                await self.create_collection(collection, dim)
            else:
                raise ValueError(f"Collection {collection} does not exist and no vectors provided")

        if not self.client:
            await self.connect()
        if not self.client:
            raise ValueError("Milvus client is not initialized")
        
        assert self.client is not None
        # 使用client方式插入向量
        try:
            # 准备数据 - 按照官方样例格式
            entities = []
            for r in records:
                # 处理metadata字段，支持JSON对象和字符串格式
                metadata_obj = r.get('metadata', {})

                # 向后兼容：如果传入的是字符串，尝试解析为JSON
                if isinstance(metadata_obj, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata_obj)
                        DebugLogger.log_debug_info("元数据兼容性", "成功解析字符串格式的metadata", "database")
                    except Exception as e:
                        DebugLogger.log_debug_info("元数据兼容性警告", f"无法解析metadata字符串，使用空字典: {e}", "database")
                        metadata_obj = {}
                elif not isinstance(metadata_obj, dict):
                    DebugLogger.log_debug_info("元数据警告", "metadata不是字典或字符串类型，已重置为空字典", "database")
                    metadata_obj = {}

                # 创建基本实体 - 按照官方样例格式
                entity = {
                    "vector": r['vector'],
                    "content": r.get('content', ''),
                    "collection_name": collection,
                    "metadata": metadata_obj  # 直接使用JSON对象
                }

                # 如果记录中包含id字段，则添加到entity中（用于自定义ID）
                if 'id' in r:
                    # 确保ID是字符串格式（支持字符串和数字ID）
                    entity['id'] = str(r['id'])
                    DebugLogger.log_debug_info("ID处理", f"使用自定义ID: {r['id']} (转换为字符串: {str(r['id'])})", "database")
                else:
                    DebugLogger.log_debug_info("ID处理", "使用自增ID", "database")

                # 添加所有额外字段作为独立列
                for key, value in r.items():
                    # 跳过已处理的标准字段，包括id字段
                    if key in ['vector', 'content', 'metadata', 'id']:
                        continue

                    # 如果是自定义字段，直接添加到entity中
                    # 注意：Milvus会自动处理动态字段
                    entity[key] = value
                    DebugLogger.log_debug_info("动态字段处理", f"添加字段 '{key}' = {value} 作为独立列", "database")

                entities.append(entity)

            # 插入数据
            await self.client.insert(
                collection_name=collection,
                data=entities
            )
            OperationLogger.log_operation_success(f"插入向量到 '{collection}'", "database", count=len(entities))
            return len(entities)
        except Exception as e:
            OperationLogger.log_operation_error(f"插入向量到 '{collection}'", e, "database")
            raise

    async def search_vectors(self, collection: str, vector: List[float], top_k: int, filter_expr: str = "") -> List[Dict]:
        """在指定集合中搜索向量，如果collection='all'则搜索所有集合

        Args:
            collection: 集合名称，如果为'all'则搜索所有集合
            vector: 查询向量
            top_k: 返回结果数量
            filter_expr: 筛选表达式，例如 "car_type='passenger'"
        """
        tracker = TimeTracker("向量搜索", category="search")
        tracker.start()
        DebugLogger.log_debug_info("开始向量搜索", f"集合: {collection}, top_k: {top_k}", "search")


        # 确保连接有效
        if self.client is None:
            DebugLogger.log_debug_info("客户端未初始化", "尝试连接...", "search")
            await self.connect()
        
        if self.client is None:
            raise RuntimeError("Database client not connected")

        # 使用client方式搜索
        try:
            # 确定要搜索的集合
            collections_to_search = []
            if collection.lower() == 'all':

                # 只有在搜索所有集合或需要验证集合存在时才刷新集合列表
                await self._refresh_collections()
                tracker.checkpoint("刷新集合列表")

                # 搜索所有集合
                collections_to_search = list(self.collections_cache)
                if not collections_to_search:
                    DebugLogger.log_debug_info("无可用集合", "搜索提前结束", "search")
                    return []
            else:
                # 搜索指定集合
                if collection not in self.collections_cache:
                    _log_manager.log("WARN", "search", f"集合不存在: '{collection}'", step=f"集合不存在: '{collection}'", action="step")
                    return []
                collections_to_search = [collection]

            # 搜索参数 (MilvusClient不需要显式指定搜索参数)

            # 打印筛选表达式
            if filter_expr:
                DebugLogger.log_debug_info("使用筛选表达式", filter_expr, "search")

            all_results = []
            for coll_name in collections_to_search:
                try:
                    # 执行搜索，使用余弦相似度
                    # 获取集合的所有字段
                    # 创建搜索参数，使用通配符获取所有字段
                    search_args = {
                        "collection_name": coll_name,
                        "data": [vector],
                        "limit": top_k,
                        "output_fields": ["*"],  # 使用通配符获取所有字段
                        "search_params": {"metric_type": "COSINE"}
                    }

                    # 如果有筛选表达式，添加到搜索参数中
                    if filter_expr:
                        search_args["filter"] = filter_expr

                    # 执行搜索
                    tracker.checkpoint(f"准备搜索-{coll_name}")
                    results = await self.client.search(**search_args)
                    tracker.checkpoint(f"执行搜索-{coll_name}")

                    # 处理结果
                    print(f"[向量库时间] 开始处理结果: {time.strftime('%H:%M:%S.%f')[:-3]}")
                    for hits in results:
                        for hit in hits:
                            entity = hit['entity']

                            # 处理metadata字段，兼容新旧两种格式
                            metadata = entity.get('metadata', {})

                            # 向后兼容：处理旧的字符串格式metadata
                            if isinstance(metadata, str):
                                try:
                                    import json
                                    metadata = json.loads(metadata)
                                    print(f"[兼容性] 成功解析旧格式的字符串metadata")
                                except Exception as e:
                                    print(f"[兼容性警告] 无法解析旧格式metadata字符串: {e}")
                                    metadata = {}
                            elif not isinstance(metadata, dict):
                                print(f"[警告] metadata不是字典或字符串类型，已重置为空字典")
                                metadata = {}

                            # 创建基本结果字典
                            result = {
                                'content': entity.get('content', ''),
                                'collection': entity.get('collection_name', coll_name),
                                'metadata': metadata,
                                'distance': float(hit.get('distance', 0)),
                                'id': hit.get('id')  # 添加ID信息
                            }

                            # 添加所有额外字段到结果中
                            for key, value in entity.items():
                                # 跳过已处理的标准字段、vector字段和id字段（id已经从hit中获取）
                                if key in ['content', 'collection_name', 'metadata', 'vector', 'id']:
                                    continue

                                # 将额外字段添加到结果中
                                result[key] = value

                            all_results.append(result)
                except Exception as e:
                    print(f"Error searching collection '{coll_name}': {e}")

            # 处理结果完成
            tracker.checkpoint("结果处理")

            # 按相似度排序并限制结果数量
            # 使用余弦相似度时，值越大表示越相似，所以需要降序排序
            all_results.sort(key=lambda x: x['distance'], reverse=True)
            limited_results = all_results[:top_k]
            tracker.checkpoint("结果排序")
            
            time_details = tracker.finish()
            DebugLogger.log_debug_info("向量搜索完成", f"返回结果数: {len(limited_results)}", "search")
            DebugLogger.log_debug_info("搜索耗时详情", time_details, "search.perf")

            return limited_results
        except Exception as e:
            OperationLogger.log_operation_error("向量搜索", e, "search")
            tracker.finish()
            return []

    async def migrate_collection_to_json_metadata(self, old_collection: str, new_collection: Optional[str] = None):
        """
        将现有集合的字符串metadata迁移到新的JSON格式集合

        Args:
            old_collection: 现有集合名称
            new_collection: 新集合名称，如果为None则使用 old_collection + "_json"
        """
        if new_collection is None:
            new_collection = f"{old_collection}_json"

        OperationLogger.log_operation_start(f"迁移集合 '{old_collection}' 到 '{new_collection}'", "migration")

        try:
            # 确保连接有效
            if self.client is None:
                await self.connect()
            if not self.client:
                raise ValueError(f"数据库客户端未初始化")

            # 检查源集合是否存在
            if old_collection not in self.collections_cache:
                await self._refresh_collections()
                if old_collection not in self.collections_cache:
                    raise ValueError(f"源集合 '{old_collection}' 不存在")

            # 查询所有数据
            OperationLogger.log_step("查询源集合数据", category="migration", details=old_collection)

            # 分批查询所有数据，避免一次性查询过多数据
            all_query_results = []
            offset = 0
            batch_size = 1000  # 每批查询1000条记录

            while True:
                # 使用空filter时必须指定limit参数
                batch_results = await self.client.query(
                    collection_name=old_collection,
                    filter="",  # 查询所有数据
                    output_fields=["*"],
                    offset=offset,
                    limit=batch_size
                )

                if not batch_results:
                    break

                all_query_results.extend(batch_results)
                OperationLogger.log_step("查询进度", category="migration", details=f"已查询 {len(all_query_results)} 条记录...")

                # 如果返回的记录数少于batch_size，说明已经查询完所有数据
                if len(batch_results) < batch_size:
                    break

                offset += batch_size

            query_results = all_query_results

            if not query_results:
                OperationLogger.log_step("源集合为空", category="migration", details=f"集合: '{old_collection}'")
                return

            OperationLogger.log_step("查询完成", category="migration", details=f"总记录数: {len(query_results)}")

            # 准备迁移数据
            migrated_records = []
            for record in query_results:
                # 获取向量维度（从第一条记录）
                if 'vector' in record and len(migrated_records) == 0:
                    dim = len(record['vector'])
                    # 创建新集合
                    await self.create_collection(new_collection, dim)

                # 处理metadata字段
                metadata = record.get('metadata', '{}')
                if isinstance(metadata, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata)
                    except:
                        metadata_obj = {}
                else:
                    metadata_obj = metadata if isinstance(metadata, dict) else {}

                # 构建新记录
                new_record = {
                    'vector': record.get('vector', []),
                    'content': record.get('content', ''),
                    'metadata': metadata_obj
                }

                # 添加其他动态字段
                for key, value in record.items():
                    if key not in ['id', 'vector', 'content', 'metadata', 'collection_name']:
                        new_record[key] = value

                migrated_records.append(new_record)

            # 批量插入到新集合
            if migrated_records:
                result = await self.insert_vectors(new_collection, migrated_records)
                OperationLogger.log_operation_success(f"迁移集合", "migration", new_collection=new_collection, migrated_count=result)
                return new_collection

        except Exception as e:
            OperationLogger.log_operation_error("迁移集合", e, "migration")
            raise

    async def upsert_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """更新或插入向量记录

        Args:
            collection: 集合名称
            records: 要更新或插入的记录列表，必须包含主键字段
        """
        if not records:
            DebugLogger.log_debug_info(f"无记录可Upsert", f"集合: {collection}", "database")
            return 0

        # 确保集合存在
        if collection not in self.collections_cache:
            if 'vector' in records[0]:
                dim = len(records[0]['vector'])
                await self.create_collection(collection, dim)
            else:
                raise ValueError(f"Collection {collection} does not exist and no vectors provided")

        if not self.client:
            await self.connect()
        if not self.client:
            raise ValueError("Milvus client is not initialized")
        
        assert self.client is not None
        # 使用client方式执行upsert操作
        try:
            # 准备数据，格式与insert_vectors相同
            entities = []
            for r in records:
                # 处理metadata字段，支持JSON对象和字符串格式
                metadata_obj = r.get('metadata', {})

                # 向后兼容：如果传入的是字符串，尝试解析为JSON
                if isinstance(metadata_obj, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata_obj)
                        DebugLogger.log_debug_info("元数据兼容性", "成功解析字符串格式的metadata", "database")
                    except Exception as e:
                        DebugLogger.log_debug_info("元数据兼容性警告", f"无法解析metadata字符串，使用空字典: {e}", "database")
                        metadata_obj = {}
                elif not isinstance(metadata_obj, dict):
                    DebugLogger.log_debug_info("元数据警告", "metadata不是字典或字符串类型，已重置为空字典", "database")
                    metadata_obj = {}

                # 对于upsert操作，需要包含主键字段
                entity = {
                    "vector": r['vector'],
                    "content": r.get('content', ''),
                    "collection_name": collection,
                    "metadata": metadata_obj  # 直接使用JSON对象
                }

                # 如果记录中包含id字段，则添加到entity中（用于upsert）
                if 'id' in r:
                    entity['id'] = r['id']

                # 添加所有额外字段作为独立列
                for key, value in r.items():
                    # 跳过已处理的标准字段
                    if key in ['vector', 'content', 'metadata', 'id']:
                        continue

                    # 如果是自定义字段，直接添加到entity中
                    entity[key] = value
                    DebugLogger.log_debug_info("动态字段处理", f"添加字段 '{key}' = {value} 作为独立列", "database")

                entities.append(entity)

            # 执行upsert操作
            result = await self.client.upsert(
                collection_name=collection,
                data=entities
            )
            upsert_count = result.get('upsert_count', len(entities)) if isinstance(result, dict) else len(entities)
            OperationLogger.log_operation_success(f"Upsert向量到 '{collection}'", "database", count=upsert_count)
            return upsert_count
        except Exception as e:
            OperationLogger.log_operation_error(f"Upsert向量到 '{collection}'", e, "database")
            raise

    async def delete_vectors(self, collection: str, ids: Optional[List[Any]] = None, filter_expr: str = ""):
        """删除向量记录

        Args:
            collection: 集合名称
            ids: 要删除的主键ID列表，如果为None则使用filter_expr
            filter_expr: 删除条件表达式，例如 "color in ['red', 'blue']"
        """
        # 确保连接有效
        if self.client is None:
            DebugLogger.log_debug_info("客户端未初始化", "尝试连接...", "database")
            await self.connect()

        if self.client is None:
            raise RuntimeError("Database client not connected")

        # 确保集合存在
        if collection not in self.collections_cache:
            await self._refresh_collections()
            if collection not in self.collections_cache:
                raise ValueError(f"Collection '{collection}' does not exist")

        try:
            if ids is not None and len(ids) > 0:
                # 按主键ID删除
                result = await self.client.delete(
                    collection_name=collection,
                    ids=ids
                )
                delete_count = result.get('delete_count', len(ids)) if isinstance(result, dict) else len(ids)
                OperationLogger.log_operation_success(f"通过ID删除实体", "database", collection=collection, count=delete_count)
                return delete_count
            elif filter_expr:
                # 按条件删除
                result = await self.client.delete(
                    collection_name=collection,
                    filter=filter_expr
                )
                delete_count = result.get('delete_count', 0) if isinstance(result, dict) else 0
                OperationLogger.log_operation_success(f"通过filter删除实体", "database", collection=collection, filter=filter_expr, count=delete_count)
                return delete_count
            else:
                raise ValueError("Either 'ids' or 'filter_expr' must be provided for deletion")

        except Exception as e:
            OperationLogger.log_operation_error(f"删除向量", e, "database", collection=collection)
            raise

    async def flush_collection(self, collection: str):
        """刷新集合数据，确保数据持久化"""
        # 确保连接有效
        if self.client is None:
            DebugLogger.log_debug_info("客户端未初始化", "尝试连接...", "database")
            await self.connect()

        if self.client is None:
            raise RuntimeError("Database client not connected")

        try:
            # 调用flush方法刷新数据
            if self.client:
                if hasattr(self.client, 'flush'):
                    await self.client.flush(collection_name=collection)
                    OperationLogger.log_operation_success(f"刷新集合 '{collection}'", "database")
                else:
                    DebugLogger.log_debug_info("flush方法不可用", "跳过刷新操作", "database")
            else:
                raise ValueError("Database client not initialized")
        except Exception as e:
            OperationLogger.log_operation_error(f"刷新集合 '{collection}'", e, "database")
            # 不抛出异常，因为flush失败不应该中断程序

    async def create_hybrid_collection(
        self, 
        collection_name: str, 
        dense_vector_dim: int, 
        auto_id: bool = True, 
        enable_dynamic_field: bool = True
    ):
        """创建支持混合检索的集合（全文检索+语义检索）

        Args:
            collection_name: 集合名称
            dense_vector_dim: 密集向量维度
            auto_id: 是否使用自增ID，默认为True
            enable_dynamic_field: 是否启用动态字段，默认为True
        """
        # 确保已连接到数据库
        if self.client is None:
            DebugLogger.log_debug_info("数据库未连接", "将在创建集合前自动连接", "hybrid_search")
            await self.connect()

        try:
            if not self.client:
                raise ValueError("Database client not initialized")

            # 检查集合是否已存在
            collections = await self.client.list_collections()
            if collection_name in collections:
                DebugLogger.log_debug_info(tag=f"混合检索集合 '{collection_name}' 已存在", info="跳过创建", category="hybrid_search")
                return collection_name

            OperationLogger.log_operation_start(f"创建混合检索集合: {collection_name}", "hybrid_search", 
                                              dense_dim=dense_vector_dim, auto_id=auto_id, dynamic_field=enable_dynamic_field)

            # 导入必要的类
            from pymilvus import DataType, Function, FunctionType

            # 创建schema（支持混合检索）
            schema = self.client.create_schema(
                auto_id=auto_id,
                enable_dynamic_field=enable_dynamic_field,
                description="Hybrid search collection with dense and sparse vectors for semantic and full-text search"
            )

            # 添加ID字段
            if auto_id:
                # 自增ID模式使用INT64
                schema.add_field(field_name="id", datatype=DataType.INT64, is_primary=True, auto_id=auto_id)
            else:
                # 自定义ID模式使用VARCHAR以支持字符串和数字
                schema.add_field(field_name="id", datatype=DataType.VARCHAR, max_length=255, is_primary=True, auto_id=auto_id)

            # 添加混合检索相关字段（与普通向量检索保持一致的字段名）
            schema.add_field(field_name="content", datatype=DataType.VARCHAR, max_length=65535, enable_analyzer=True, description="文本内容，用于存储和全文检索（非加密）")
            schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=dense_vector_dim, description="密集向量用于语义检索")
            schema.add_field(field_name="text_sparse", datatype=DataType.SPARSE_FLOAT_VECTOR, description="稀疏向量用于全文检索（BM25自动生成）")
            
            # 添加其他字段
            schema.add_field(field_name="collection_name", datatype=DataType.VARCHAR, max_length=255)
            schema.add_field(field_name="metadata", datatype=DataType.JSON, description="JSON格式的元数据")

            # 添加BM25函数以自动生成稀疏向量
            try:
                bm25_function = Function(
                    name="content_bm25_emb",
                    input_field_names=["content"],  # 使用content字段作为输入
                    output_field_names=["text_sparse"],  # 输出字段
                    function_type=FunctionType.BM25,
                )
                schema.add_function(bm25_function)
                DebugLogger.log_debug_info("成功添加BM25函数", "输入字段: content", "hybrid_search")
            except Exception as func_error:
                _log_manager.log("WARN", "hybrid_search", "添加BM25函数失败", details=str(func_error), action="step")

            # 创建索引参数
            index_params = self.client.prepare_index_params()

            # 为密集向量字段创建索引（语义检索）
            index_params.add_index(
                field_name="vector",
                index_type="AUTOINDEX",
                metric_type="COSINE"
            )

            # 为稀疏向量字段创建索引（全文检索）
            try:
                index_params.add_index(
                    field_name="text_sparse",
                    index_type="SPARSE_INVERTED_INDEX",
                    metric_type="BM25"  # BM25度量类型，用于BM25函数输出字段
                )
                DebugLogger.log_debug_info(tag="成功添加稀疏向量索引", info="", category="hybrid_search")
            except Exception as sparse_idx_error:
                _log_manager.log("WARN", "hybrid_search", "添加稀疏向量索引失败", details=str(sparse_idx_error), action="step")

            # 为content字段创建全文检索倒排索引
            try:
                index_params.add_index(
                    field_name="content",
                    index_type="INVERTED"
                )
                DebugLogger.log_debug_info(tag="成功为content字段添加全文检索索引", info="", category="hybrid_search")
            except Exception as content_idx_error:
                _log_manager.log("WARN", "hybrid_search", "添加content字段全文检索索引失败", details=str(content_idx_error), action="step")

            # 创建集合
            await self.client.create_collection(
                collection_name=collection_name,
                schema=schema,
                index_params=index_params
            )
            OperationLogger.log_operation_success(f"成功创建混合检索集合 '{collection_name}'", "hybrid_search")

            # 加载集合
            try:
                if hasattr(self.client, 'load_collection'):
                    await self.client.load_collection(collection_name=collection_name)
                    DebugLogger.log_debug_info(tag=f"成功加载集合 '{collection_name}'", info="", category="hybrid_search")
            except AttributeError as e:
                DebugLogger.log_debug_info(tag="load_collection方法不可用", info=str(e), category="hybrid_search")
                # 在某些版本的MilvusClient中，创建集合后会自动加载

            # 更新集合缓存
            self.collections_cache.add(collection_name)
            OperationLogger.log_operation_success(f"混合检索集合 '{collection_name}' 创建完成", "hybrid_search", details="支持: 语义检索(密集向量) + 全文检索(稀疏向量/BM25)")
            return collection_name

        except Exception as e:
            OperationLogger.log_operation_error("创建混合检索集合", e, "hybrid_search")
            raise

    async def insert_hybrid_vectors(self, collection: str, records: List[Dict[str, Any]]):
        """插入混合检索向量到指定集合

        Args:
            collection: 集合名称
            records: 记录列表，每个记录应包含:
                - content: 文本内容（用于存储和BM25全文检索，非加密）
                - vector: 密集向量（用于语义检索）
                - metadata: JSON格式的元数据
                - 其他动态字段
        """
        if not records:
            DebugLogger.log_debug_info(f"无记录可插入", f"集合: {collection}", "hybrid_search")
            return 0

        # 确保集合存在
        if collection not in self.collections_cache:
            if 'vector' in records[0]:
                dim = len(records[0]['vector'])
                await self.create_hybrid_collection(collection, dim)
            else:
                raise ValueError(f"Collection {collection} does not exist and no vectors provided")

        if not self.client:
            await self.connect()
        if not self.client:
            raise ValueError("Milvus client is not initialized")
            
        assert self.client is not None
        try:
            OperationLogger.log_operation_start(f"插入混合检索记录到 '{collection}'", "hybrid_search", count=len(records))
            
            # 准备混合检索数据
            entities = []
            for i, r in enumerate(records):
                # 处理metadata字段，支持JSON对象和字符串格式
                metadata_obj = r.get('metadata', {})

                # 向后兼容：如果传入的是字符串，尝试解析为JSON
                if isinstance(metadata_obj, str):
                    try:
                        import json
                        metadata_obj = json.loads(metadata_obj)
                        DebugLogger.log_debug_info("元数据兼容性", "成功解析字符串格式的metadata", "hybrid_search")
                    except Exception as e:
                        DebugLogger.log_debug_info("元数据兼容性警告", f"无法解析metadata字符串，使用空字典: {e}", "hybrid_search")
                        metadata_obj = {}
                elif not isinstance(metadata_obj, dict):
                    DebugLogger.log_debug_info("元数据警告", "metadata不是字典或字符串类型，已重置为空字典", "hybrid_search")
                    metadata_obj = {}

                # 创建混合检索实体
                entity = {
                    "content": r.get('content', ''),  # 文本内容，用于存储和BM25全文检索（非加密）
                    "vector": r.get('vector', []),  # 密集向量用于语义检索
                    # text_sparse 由BM25函数自动生成，不需要手动提供
                    "collection_name": collection,
                    "metadata": metadata_obj  # JSON格式的元数据
                }

                # 如果记录中包含id字段，则添加到entity中（用于自定义ID）
                if 'id' in r:
                    entity['id'] = r['id']
                    DebugLogger.log_debug_info("ID处理", f"记录 {i+1} 使用自定义ID: {r['id']}", "hybrid_search")

                # 添加所有额外字段作为独立列
                for key, value in r.items():
                    # 跳过已处理的标准字段
                    if key in ['content', 'vector', 'text_sparse', 'metadata', 'id']:
                        continue

                    # 如果是自定义字段，直接添加到entity中
                    entity[key] = value
                    DebugLogger.log_debug_info("动态字段处理", f"记录 {i+1} 添加字段 '{key}' = {value}", "hybrid_search")

                entities.append(entity)

                # 验证必要字段
                if not entity.get('content'):
                    DebugLogger.log_debug_info("数据验证警告", f"记录 {i+1} 缺少content字段，BM25功能可能无法正常工作", "hybrid_search")
                if not entity.get('vector'):
                    DebugLogger.log_debug_info("数据验证警告", f"记录 {i+1} 缺少vector字段，语义检索功能可能无法正常工作", "hybrid_search")

            # 插入混合检索数据
            if self.client is None:
                raise RuntimeError("Database client not connected")

            await self.client.insert(
                collection_name=collection,
                data=entities
            )
            OperationLogger.log_operation_success(f"插入混合检索记录到 '{collection}'", "hybrid_search", count=len(entities))
            return len(entities)

        except Exception as e:
            OperationLogger.log_operation_error("插入混合检索向量", e, "hybrid_search")
            raise

    def _format_search_results(self, results, collection: str, search_type: str, rerank_method: Optional[str] = None) -> List[Dict]:
        """
        通用的搜索结果格式化方法，排除大型字段并统一结果格式
        
        Args:
            results: Milvus搜索结果
            collection: 集合名称
            search_type: 搜索类型 ("semantic", "full_text", "hybrid_official")
            rerank_method: 重排序方法（可选，用于混合检索）
            
        Returns:
            格式化后的结果列表
        """
        formatted_results = []
        if results and len(results) > 0:
            for hit in results[0]:
                entity = hit.get("entity", {})
                
                # 处理metadata字段，兼容新旧两种格式
                metadata = entity.get('metadata', {})
                if isinstance(metadata, str):
                    try:
                        import json
                        metadata = json.loads(metadata)
                    except Exception as e:
                        DebugLogger.log_debug_info(f"{search_type}-兼容性警告", f"无法解析metadata字符串: {e}", "search")
                        metadata = {}
                elif not isinstance(metadata, dict):
                    metadata = {}
                
                # 创建基本结果字典，排除vector和text_sparse等大型字段
                result = {
                    'content': entity.get('content', ''),
                    'collection': entity.get('collection_name', collection),
                    'metadata': metadata,
                    'distance': float(hit.get('distance', 0)),
                    'id': hit.get('id'),
                    'search_type': search_type
                }
                
                # 如果有重排序方法信息，添加到结果中
                if rerank_method:
                    result['rerank_method'] = rerank_method
                
                # 添加其他额外字段，但排除大型字段
                for key, value in entity.items():
                    # 跳过已处理的字段和大型字段（vector, text_sparse等）
                    if key in ['content', 'collection_name', 'metadata', 'vector', 'text_sparse', 'id']:
                        continue
                    result[key] = value
                
                formatted_results.append(result)
        
        return formatted_results

    async def search_hybrid_vectors(
        self,
        collection: str,
        query_text: str,
        dense_vector: List[float],
        top_k: int,
        filter_expr: str = "",
        search_strategy: str = "hybrid",
        rerank_strategy: str = "rrf",
        rrf_k: int = 60,
        dense_weight: float = 0.6,
        sparse_weight: float = 0.4
    ) -> List[Dict]:
        """混合检索：结合语义检索和全文检索（使用Milvus官方API）
        
        Args:
            collection: 集合名称（必须支持混合检索）
            query_text: 原始查询文本
            dense_vector: 密集向量（用于语义检索）
            top_k: 返回结果数量
            filter_expr: 筛选表达式
            search_strategy: 检索策略 ("semantic", "full_text", "hybrid")
            rerank_strategy: 重排序策略 ("rrf", "weighted")
            rrf_k: RRF平滑参数k，范围[10,100]，仅在rerank_strategy='rrf'时使用
            dense_weight: 语义检索权重(0-1)，仅在rerank_strategy='weighted'时使用
            sparse_weight: 全文检索权重(0-1)，仅在rerank_strategy='weighted'时使用
            
        Returns:
            搜索结果列表
            
        Raises:
            ValueError: 如果集合不支持指定的检索策略
            RuntimeError: 如果数据库连接失败
        """
        OperationLogger.log_operation_start("混合检索", "hybrid_search", collection=collection, strategy=search_strategy, top_k=top_k)
        
        # 确保连接有效
        if self.client is None:
            DebugLogger.log_debug_info("客户端未初始化", "尝试连接...", "hybrid_search")
            await self.connect()

        if self.client is None:
            raise RuntimeError("Database client not connected")

        try:
            if search_strategy == "semantic":
                # 仅语义检索（密集向量）
                DebugLogger.log_debug_info("执行语义检索", "仅密集向量", "hybrid_search")
                return await self._semantic_search_only(collection, dense_vector, top_k, filter_expr)
                
            elif search_strategy == "full_text":
                # 仅全文检索（稀疏向量）
                DebugLogger.log_debug_info("执行全文检索", "仅稀疏向量", "hybrid_search")
                return await self._full_text_search_only(collection, query_text, top_k, filter_expr)
                
            elif search_strategy == "hybrid":
                # 使用Milvus官方混合检索API
                DebugLogger.log_debug_info("执行混合检索", "使用官方API", "hybrid_search")
                if rerank_strategy == "rrf":
                    DebugLogger.log_debug_info("RRF配置", f"k={rrf_k}", "hybrid_search")
                else:
                    DebugLogger.log_debug_info("权重配置", f"语义={dense_weight}, 全文={sparse_weight}", "hybrid_search")
                return await self._official_hybrid_search(
                    collection, query_text, dense_vector, top_k, filter_expr,
                    rerank_strategy, rrf_k, dense_weight, sparse_weight
                )
            else:
                raise ValueError(f"不支持的检索策略: {search_strategy}")
                
        except Exception as e:
            OperationLogger.log_operation_error("混合检索", e, "hybrid_search")
            raise

    async def _semantic_search_only(self, collection: str, dense_vector: List[float], top_k: int, filter_expr: str = "") -> List[Dict]:
        """仅执行语义检索（密集向量）"""
        try:
            if self.client is None:
                raise RuntimeError("Database client not connected")
            
            # 官方推荐写法
            search_args = {
                "collection_name": collection,
                "data": [dense_vector],
                "anns_field": "vector", 
                "search_params": {
                    "metric_type": "COSINE",
                    "params": {}
                },
                "limit": top_k,
                "output_fields": ["*"]
            }
            if filter_expr:
                search_args["filter"] = filter_expr

            results = await self.client.search(**search_args)

            # 使用通用结果处理方法
            formatted_results = self._format_search_results(results, collection, "semantic")

            OperationLogger.log_operation_success("语义检索", "hybrid_search.semantic", result_count=len(formatted_results))
            return formatted_results
            
        except Exception as e:
            OperationLogger.log_operation_error("语义检索", e, "hybrid_search.semantic")
            raise

    async def _full_text_search_only(self, collection: str, query_text: str, top_k: int, filter_expr: str = "") -> List[Dict]:
        """仅执行全文检索（稀疏向量/BM25）"""
        try:
            if self.client is None:
                raise RuntimeError("Database client not connected")
                
            DebugLogger.log_debug_info("执行BM25全文检索", f"查询文本: '{query_text}'", "hybrid_search.full_text")
                
            # 对于BM25函数生成的稀疏向量，使用文本直接查询
            # 让BM25函数自动处理文本到稀疏向量的转换
            search_params = {
                "metric_type": "BM25",
                "params": {}
            }
            
            # 构建搜索参数字典 - 直接使用文本查询
            search_args = {
                "collection_name": collection,
                "data": [query_text],  # 直接传入文本，让BM25函数处理
                "anns_field": "text_sparse",  # 指定BM25函数输出的稀疏向量字段
                "search_params": search_params,
                "limit": top_k,
                "output_fields": ["*"]
            }
            
            # 如果有筛选表达式，添加filter参数
            if filter_expr:
                search_args["filter"] = filter_expr
            
            DebugLogger.log_debug_info("BM25搜索参数", search_args, "hybrid_search.full_text")
            
            # 执行BM25全文搜索
            results = await self.client.search(**search_args)

            # 使用通用结果处理方法
            formatted_results = self._format_search_results(results, collection, "full_text")

            OperationLogger.log_operation_success("BM25全文检索", "hybrid_search.full_text", result_count=len(formatted_results))
            return formatted_results
                
        except Exception as e:
            OperationLogger.log_operation_error("全文检索", e, "hybrid_search.full_text")
            # 尝试备用方法：使用 AnnSearchRequest 方式
            try:
                DebugLogger.log_debug_info(tag="全文检索失败，尝试备用方法", info=str(e), category="hybrid_search.full_text")
                from pymilvus import AnnSearchRequest
                
                # 创建搜索请求（虽然这里没有直接使用，但保留以备将来使用）
                _ = AnnSearchRequest(
                    data=[query_text],
                    anns_field="text_sparse",
                    param={"metric_type": "BM25"},
                    limit=top_k,
                    expr=filter_expr if filter_expr else None
                )
                
                if self.client is None:
                    raise RuntimeError("Database client not connected")

                # 使用单个搜索请求
                search_args = {
                    "collection_name": collection,
                    "data": [query_text],
                    "anns_field": "text_sparse",
                    "search_params": {"metric_type": "BM25", "params": {}},
                    "limit": top_k,
                    "output_fields": ["*"]
                }
                if filter_expr:
                    search_args["filter"] = filter_expr

                results = await self.client.search(**search_args)

                # 使用通用结果处理方法
                formatted_results = self._format_search_results(results, collection, "full_text")

                OperationLogger.log_operation_success("全文检索(备用方法)", "hybrid_search.full_text", result_count=len(formatted_results))
                return formatted_results
                
            except Exception as backup_error:
                OperationLogger.log_operation_error("全文检索(备用方法)", backup_error, "hybrid_search.full_text")
                raise ValueError(f"集合 '{collection}' 不支持全文检索，错误: {str(e)}。备用方法也失败: {str(backup_error)}")

    async def _official_hybrid_search(
        self, 
        collection: str, 
        query_text: str, 
        dense_vector: List[float], 
        top_k: int, 
        filter_expr: str = "",
        rerank_strategy: str = "rrf",
        rrf_k: int = 60,
        dense_weight: float = 0.6,
        sparse_weight: float = 0.4
    ) -> List[Dict]:
        """使用Milvus官方API执行混合检索"""
        try:
            DebugLogger.log_debug_info(tag="开始使用官方API执行混合检索", info="", category="hybrid_search.official")
            
            if self.client is None:
                raise RuntimeError("Database client not connected")
            
            # 导入Milvus官方Ranker和AnnSearchRequest
            from pymilvus import RRFRanker, WeightedRanker, AnnSearchRequest
            DebugLogger.log_debug_info(tag="成功导入官方Ranker和AnnSearchRequest", info="", category="hybrid_search.official")
            
            # 创建搜索请求列表（使用AnnSearchRequest对象）
            search_requests = []
            
            # 1. 密集向量搜索请求（语义检索）
            dense_search_params = {"metric_type": "COSINE"}
            dense_req = AnnSearchRequest(
                data=[dense_vector],
                anns_field="vector",
                param=dense_search_params,
                limit=top_k * 2,  # 扩大搜索范围用于重排序
                expr=filter_expr if filter_expr else None
            )
            search_requests.append(dense_req)
            
            # 2. 稀疏向量搜索请求（全文检索）
            sparse_search_params = {"metric_type": "BM25"}
            sparse_req = AnnSearchRequest(
                data=[query_text],  # 使用原始文本，让BM25函数处理
                anns_field="text_sparse",
                param=sparse_search_params,
                limit=top_k * 2,
                expr=filter_expr if filter_expr else None
            )
            search_requests.append(sparse_req)
            
            # 创建Ranker（根据官方文档）
            if rerank_strategy == "rrf":
                # RRF策略：使用传入的k值，范围[10,100]
                ranker = RRFRanker(rrf_k)
                DebugLogger.log_debug_info("使用RRF重排序策略", f"k={rrf_k}", "hybrid_search.official")
            elif rerank_strategy == "weighted":
                # WeightedRanker策略：权重范围[0,1]，权重越高越重要
                ranker = WeightedRanker(dense_weight, sparse_weight)
                DebugLogger.log_debug_info("使用加权重排序策略", f"密集权重={dense_weight}, 稀疏权重={sparse_weight}", "hybrid_search.official")
            else:
                DebugLogger.log_debug_info(f"不支持的重排序策略 '{rerank_strategy}'", "使用RRF作为备用", "hybrid_search.official")
                ranker = RRFRanker(rrf_k)
            
            # 执行官方混合检索
            DebugLogger.log_debug_info("执行hybrid_search", f"请求数: {len(search_requests)}", "hybrid_search.official")
            results = await self.client.hybrid_search(
                collection_name=collection,
                reqs=search_requests,
                ranker=ranker,
                limit=top_k,
                output_fields=["*"]  # 返回所有字段
            )

            # 使用通用结果处理方法
            formatted_results = self._format_search_results(results, collection, "hybrid_official", rerank_strategy)

            OperationLogger.log_operation_success("官方混合检索", "hybrid_search.official", result_count=len(formatted_results))
            return formatted_results
            
        except Exception as e:
            OperationLogger.log_operation_error("官方混合检索", e, "hybrid_search.official")
            raise ValueError(f"集合 '{collection}' 不支持混合检索或缺少必要字段 ('vector', 'text_sparse')，错误: {str(e)}")

